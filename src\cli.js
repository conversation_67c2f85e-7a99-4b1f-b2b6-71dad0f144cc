#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const inquirer = require('inquirer');
const ora = require('ora');
const path = require('path');
const fs = require('fs-extra');

// Import core modules
const Agent = require('./core/agent');
const ConfigManager = require('./config/config');
const Logger = require('./utils/logger');
const { handleError } = require('./utils/error-handler');

const program = new Command();
const logger = new Logger();

// ASCII Art Banner
const banner = `
${chalk.cyan('╔══════════════════════════════════════════════════════════════╗')}
${chalk.cyan('║')}                    ${chalk.bold.yellow('AI CLI AGENT')}                        ${chalk.cyan('║')}
${chalk.cyan('║')}              ${chalk.gray('Autonomous AI-Powered CLI Tool')}              ${chalk.cyan('║')}
${chalk.cyan('╚══════════════════════════════════════════════════════════════╝')}
`;

// Initialize the CLI
async function initializeCLI() {
  try {
    // Load configuration
    const config = await ConfigManager.load();

    // Initialize agent
    const agent = new Agent(config);
    await agent.initialize();

    return { agent, config };
  } catch (error) {
    handleError(error, 'Failed to initialize CLI');
    process.exit(1);
  }
}

// Main CLI setup
program
  .name('ai-cli')
  .description('Autonomous AI-Powered CLI Tool System')
  .version('1.0.0')
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-q, --quiet', 'Suppress non-essential output')
  .option('--config <path>', 'Path to configuration file')
  .hook('preAction', async (thisCommand) => {
    if (thisCommand.opts().verbose) {
      logger.setLevel('debug');
    }
    if (thisCommand.opts().quiet) {
      logger.setLevel('error');
    }
  });

// Chat command - Interactive conversation with AI
program
  .command('chat')
  .description('Start an interactive chat session with the AI agent')
  .option('-m, --model <model>', 'Specify LLM model to use')
  .option('-p, --provider <provider>', 'Specify LLM provider')
  .action(async (options) => {
    console.log(banner);
    
    const spinner = ora('Initializing AI Agent...').start();
    const { agent } = await initializeCLI();
    spinner.succeed('AI Agent ready!');
    
    console.log(chalk.green('\n🤖 AI Agent is ready to help! Type "exit" to quit.\n'));
    
    while (true) {
      try {
        const { message } = await inquirer.prompt([
          {
            type: 'input',
            name: 'message',
            message: chalk.blue('You:'),
            validate: (input) => input.trim() !== '' || 'Please enter a message'
          }
        ]);
        
        if (message.toLowerCase() === 'exit') {
          console.log(chalk.yellow('\n👋 Goodbye!'));
          break;
        }
        
        const thinkingSpinner = ora('AI is thinking...').start();
        const response = await agent.processMessage(message, options);
        thinkingSpinner.stop();
        
        console.log(chalk.green('\n🤖 AI:'), response);
        console.log();
        
      } catch (error) {
        handleError(error, 'Chat session error');
      }
    }
  });

// Execute command - One-shot command execution
program
  .command('exec <task>')
  .description('Execute a task autonomously')
  .option('-y, --yes', 'Skip confirmation prompts')
  .option('-d, --dry-run', 'Show what would be done without executing')
  .option('--parallel', 'Enable parallel execution when possible')
  .action(async (task, options) => {
    console.log(banner);
    
    const spinner = ora('Analyzing task...').start();
    const { agent } = await initializeCLI();
    
    try {
      const plan = await agent.planTask(task);
      spinner.succeed('Task analysis complete!');
      
      console.log(chalk.cyan('\n📋 Execution Plan:'));
      plan.steps.forEach((step, index) => {
        console.log(chalk.gray(`  ${index + 1}. ${step.description}`));
      });
      
      if (options.dryRun) {
        console.log(chalk.yellow('\n🔍 Dry run complete. No actions were executed.'));
        return;
      }
      
      if (!options.yes) {
        const { confirm } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: 'Do you want to proceed with this plan?',
            default: true
          }
        ]);
        
        if (!confirm) {
          console.log(chalk.yellow('Task cancelled.'));
          return;
        }
      }
      
      const executeSpinner = ora('Executing task...').start();
      const result = await agent.executeTask(plan, options);
      executeSpinner.succeed('Task completed successfully!');
      
      console.log(chalk.green('\n✅ Results:'));
      console.log(result.summary);
      
    } catch (error) {
      spinner.fail('Task execution failed');
      handleError(error, 'Task execution error');
    }
  });

// Config command - Manage configuration
program
  .command('config')
  .description('Manage AI CLI configuration')
  .option('--setup', 'Run initial setup wizard')
  .option('--show', 'Show current configuration')
  .option('--reset', 'Reset configuration to defaults')
  .action(async (options) => {
    const config = await ConfigManager.load();
    
    if (options.setup) {
      await ConfigManager.runSetupWizard();
    } else if (options.show) {
      console.log(chalk.cyan('Current Configuration:'));
      console.log(JSON.stringify(config, null, 2));
    } else if (options.reset) {
      await ConfigManager.reset();
      console.log(chalk.green('Configuration reset to defaults.'));
    } else {
      console.log(chalk.yellow('Use --setup, --show, or --reset'));
    }
  });

// Status command - Show system status
program
  .command('status')
  .description('Show AI CLI system status')
  .option('--detailed', 'Show detailed status information')
  .action(async (options) => {
    const { agent } = await initializeCLI();
    const status = await agent.getSystemStatus();

    console.log(chalk.cyan('\n🔍 System Status:'));
    console.log(`Provider: ${chalk.yellow(status.provider)}`);
    console.log(`Model: ${chalk.yellow(status.model)}`);
    console.log(`Context: ${chalk.yellow(status.contextSize)} items`);
    console.log(`Tools: ${chalk.yellow(status.availableTools)} available`);
    console.log(`Status: ${status.healthy ? chalk.green('Healthy') : chalk.red('Issues detected')}`);

    if (options.detailed) {
      console.log(chalk.cyan('\n📊 Detailed Information:'));
      console.log(`Current Task: ${status.currentTask || 'None'}`);
      console.log(`Execution History: ${status.executionHistory} tasks`);

      // Show tool status
      const toolRegistry = agent.toolRegistry;
      const toolStats = toolRegistry.getToolUsageStats();

      console.log(chalk.cyan('\n🔧 Tool Statistics:'));
      Object.entries(toolStats).forEach(([name, stats]) => {
        console.log(`  ${name}: ${stats.executionCount} uses, last used: ${stats.lastUsed || 'Never'}`);
      });
    }
  });

// Monitor command - Real-time system monitoring
program
  .command('monitor')
  .description('Start real-time system monitoring')
  .option('-d, --duration <seconds>', 'Monitoring duration in seconds', '60')
  .option('-i, --interval <seconds>', 'Sampling interval in seconds', '5')
  .action(async (options) => {
    console.log(banner);

    const { agent } = await initializeCLI();
    const spinner = ora('Starting system monitoring...').start();

    try {
      const result = await agent.toolRegistry.executeTool('monitoring-analytics', {
        operation: 'real_time_metrics',
        duration: parseInt(options.duration),
        interval: parseInt(options.interval)
      });

      spinner.succeed('Monitoring completed!');

      console.log(chalk.cyan('\n📊 Monitoring Results:'));
      console.log(`Duration: ${result.result.duration}s`);
      console.log(`Samples: ${result.result.samplesCollected}`);

      const summary = result.result.summary;
      if (summary.cpu) {
        console.log(chalk.cyan('\n🖥️  CPU Summary:'));
        console.log(`  Average: ${summary.cpu.avg.toFixed(1)}%`);
        console.log(`  Peak: ${summary.cpu.max.toFixed(1)}%`);
        console.log(`  Trend: ${summary.cpu.trend}`);
      }

      if (summary.memory) {
        console.log(chalk.cyan('\n💾 Memory Summary:'));
        console.log(`  Average: ${summary.memory.avg.toFixed(1)}%`);
        console.log(`  Peak: ${summary.memory.max.toFixed(1)}%`);
        console.log(`  Trend: ${summary.memory.trend}`);
      }

    } catch (error) {
      spinner.fail('Monitoring failed');
      handleError(error, 'System monitoring error');
    }
  });

// Chain command - Execute multiple tasks in sequence
program
  .command('chain <tasks...>')
  .description('Execute multiple tasks in sequence')
  .option('--continue-on-error', 'Continue execution even if a task fails')
  .option('--parallel', 'Execute tasks in parallel when possible')
  .action(async (tasks, options) => {
    console.log(banner);

    const spinner = ora('Initializing task chain...').start();
    const { agent } = await initializeCLI();
    spinner.succeed('Task chain ready!');

    console.log(chalk.cyan(`\n🔗 Executing ${tasks.length} tasks:`));
    tasks.forEach((task, index) => {
      console.log(chalk.gray(`  ${index + 1}. ${task}`));
    });

    try {
      const result = await agent.chainTasks(tasks, {
        continueOnError: options.continueOnError,
        parallel: options.parallel
      });

      console.log(chalk.green('\n✅ Task Chain Results:'));
      console.log(result.summary);

      if (!result.success) {
        const failedTasks = result.results.filter(r => r.error);
        console.log(chalk.red('\n❌ Failed Tasks:'));
        failedTasks.forEach(task => {
          console.log(chalk.red(`  ${task.index + 1}. ${task.task}: ${task.error}`));
        });
      }

    } catch (error) {
      handleError(error, 'Task chain execution error');
    }
  });

// Plugin command - Manage plugins
program
  .command('plugin')
  .description('Manage AI CLI plugins')
  .option('--list', 'List all installed plugins')
  .option('--install <source>', 'Install a plugin from source')
  .option('--unload <name>', 'Unload a plugin')
  .option('--create <name>', 'Create a new plugin')
  .option('--stats', 'Show plugin statistics')
  .action(async (options) => {
    console.log(banner);

    const { agent } = await initializeCLI();

    try {
      if (options.list) {
        const plugins = agent.getAvailablePlugins();
        console.log(chalk.cyan('\n🔌 Installed Plugins:'));

        if (plugins.length === 0) {
          console.log(chalk.gray('  No plugins installed'));
        } else {
          plugins.forEach(plugin => {
            console.log(`  ${chalk.yellow(plugin.name)} v${plugin.version}`);
            console.log(`    ${chalk.gray(plugin.description)}`);
            console.log(`    Category: ${plugin.category || 'general'}`);
          });
        }
      }

      if (options.install) {
        const spinner = ora(`Installing plugin from ${options.install}...`).start();
        try {
          const pluginName = await agent.installPlugin(options.install);
          spinner.succeed(`Plugin installed: ${pluginName}`);
        } catch (error) {
          spinner.fail('Plugin installation failed');
          throw error;
        }
      }

      if (options.unload) {
        const spinner = ora(`Unloading plugin: ${options.unload}...`).start();
        try {
          await agent.unloadPlugin(options.unload);
          spinner.succeed(`Plugin unloaded: ${options.unload}`);
        } catch (error) {
          spinner.fail('Plugin unload failed');
          throw error;
        }
      }

      if (options.create) {
        const spinner = ora(`Creating plugin: ${options.create}...`).start();
        try {
          const pluginPath = await agent.createPlugin(options.create);
          spinner.succeed(`Plugin created at: ${pluginPath}`);
          console.log(chalk.cyan('\n📝 Next steps:'));
          console.log(`  1. Edit the plugin code in: ${pluginPath}`);
          console.log(`  2. Test your plugin`);
          console.log(`  3. Load it with: ai-cli plugin --load ${pluginPath}`);
        } catch (error) {
          spinner.fail('Plugin creation failed');
          throw error;
        }
      }

      if (options.stats) {
        const stats = agent.getPluginStats();
        console.log(chalk.cyan('\n📊 Plugin Statistics:'));
        console.log(`Total Plugins: ${stats.total}`);
        console.log(`Active Plugins: ${stats.byStatus.active}`);

        if (Object.keys(stats.byCategory).length > 0) {
          console.log(chalk.cyan('\nBy Category:'));
          Object.entries(stats.byCategory).forEach(([category, count]) => {
            console.log(`  ${category}: ${count}`);
          });
        }
      }

      if (!options.list && !options.install && !options.unload && !options.create && !options.stats) {
        console.log(chalk.yellow('\nUse --list, --install, --unload, --create, or --stats'));
      }

    } catch (error) {
      handleError(error, 'Plugin management error');
    }
  });

// Health command - System health check
program
  .command('health')
  .description('Perform comprehensive system health check')
  .option('--detailed', 'Show detailed health information')
  .action(async (options) => {
    console.log(banner);

    const spinner = ora('Performing health check...').start();
    const { agent } = await initializeCLI();

    try {
      const health = await agent.healthCheck();

      if (health.overall) {
        spinner.succeed('System is healthy!');
        console.log(chalk.green('\n✅ All systems operational'));
      } else {
        spinner.fail('System health issues detected');
        console.log(chalk.red('\n❌ Health issues found'));
      }

      if (options.detailed) {
        console.log(chalk.cyan('\n🔍 Component Health:'));
        Object.entries(health.components).forEach(([component, status]) => {
          const icon = status === true ? '✅' : '❌';
          const color = status === true ? chalk.green : chalk.red;

          if (typeof status === 'object') {
            console.log(`  ${component}: ${icon}`);
            Object.entries(status).forEach(([subComponent, subStatus]) => {
              const subIcon = subStatus ? '✅' : '❌';
              console.log(`    ${subComponent}: ${subIcon}`);
            });
          } else {
            console.log(`  ${component}: ${icon} ${color(status ? 'Healthy' : 'Issues')}`);
          }
        });
      }

    } catch (error) {
      spinner.fail('Health check failed');
      handleError(error, 'Health check error');
    }
  });

// Error handling
process.on('uncaughtException', (error) => {
  handleError(error, 'Uncaught exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  handleError(reason, 'Unhandled promise rejection');
  process.exit(1);
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  console.log(banner);
  program.outputHelp();
}
